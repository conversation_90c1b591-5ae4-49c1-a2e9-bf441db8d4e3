<template>
  <uni-popup ref="changeEngineerDialog" type="center" :mask-click="false">
    <view class="change-engineer-dialog">
      <view class="dialog-header">
        <text class="dialog-title">更换工程师或更改日期</text>
        <text class="close-btn" @click="handleClose">✕</text>
      </view>
      
      <view class="dialog-content">
        <view class="form-container">
          <!-- 打样单编号 -->
          <view class="form-item">
            <text class="form-label">打样单编号</text>
            <u-input
              v-model="formData.sampleOrderCode"
              disabled
              customStyle="background-color: #f5f5f5;"
            />
          </view>

          <!-- 当前工程师 -->
          <view class="form-item">
            <text class="form-label">当前工程师</text>
            <u-input
              v-model="formData.currentEngineerName"
              disabled
              customStyle="background-color: #f5f5f5;"
            />
          </view>

          <!-- 选择工程师类型 -->
          <view class="form-item">
            <text class="form-label">选择工程师</text>
            <u-radio-group v-model="engineerSelectType" placement="row">
              <u-radio
                :customStyle="{marginBottom: '8px'}"
                label="specified"
                :disabled="loading"
              >
                指定部门工程师
              </u-radio>
              <u-radio
                :customStyle="{marginBottom: '8px'}"
                label="other"
                :disabled="loading"
              >
                其他部门工程师
              </u-radio>
            </u-radio-group>
          </view>

          <!-- 工程师选择 -->
          <view class="form-item">
            <text class="form-label">选择工程师 <text class="required">*</text></text>
            <u-select
              v-model="formData.newEngineerId"
              :list="currentEngineerOptions"
              :placeholder="engineerSelectType === 'specified' ? '请选择指定部门工程师' : '请选择其他部门工程师'"
              :loading="engineerLoading"
              @change="handleEngineerChange"
            />
          </view>

          <!-- 选择日期 -->
          <view class="form-item">
            <text class="form-label">选择指定日期 <text class="required">*</text></text>
            <u-datetime-picker
              ref="datetimePicker"
              v-model="formData.scheduledDate"
              mode="datetime"
              :minDate="minDate"
              :formatter="dateFormatter"
              @confirm="handleDateConfirm"
            />
            <u-input
              v-model="scheduledDateText"
              placeholder="请选择日期时间"
              readonly
              @click="openDatePicker"
              suffixIcon="calendar"
            />
          </view>

          <!-- 重新安排工作开关 -->
          <view class="form-item">
            <text class="form-label">是否重新安排工作</text>
            <view class="switch-container">
              <u-switch
                v-model="formData.adjustWorkSchedule"
                :activeValue="1"
                :inactiveValue="0"
                activeText="是"
                inactiveText="否"
                size="25"
              />
            </view>
          </view>
        </view>
      </view>

      <view class="dialog-footer">
        <u-button
          @click="handleClose"
          size="default"
          customStyle="flex: 1; margin-right: 10rpx;"
          :disabled="loading"
        >
          取消
        </u-button>
        <u-button
          type="primary"
          @click="handleConfirm"
          size="default"
          customStyle="flex: 1;"
          :loading="loading"
        >
          确定
        </u-button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, reactive, computed, inject, watch } from 'vue'

const dayjs: any = inject('dayjs')

// Props
interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  confirm: [data: any]
  close: []
}>()

// Refs
const changeEngineerDialog = ref()
const datetimePicker = ref()

// 响应式数据
const loading = ref(false)
const engineerLoading = ref(false)
const engineerSelectType = ref('specified')

// 表单数据
const formData = reactive({
  id: '',
  sampleOrderCode: '',
  currentEngineerName: '',
  newEngineerId: '',
  scheduledDate: '',
  adjustWorkSchedule: 0
})

// 工程师选项
const specifiedEngineers = ref([])
const otherEngineers = ref([])

// 计算当前工程师选项
const currentEngineerOptions = computed(() => {
  const options = engineerSelectType.value === 'specified' ? specifiedEngineers.value : otherEngineers.value
  return options.map(engineer => ({
    label: engineer.nickName,
    value: engineer.userId
  }))
})

// 日期相关
const minDate = ref(Date.now())
const scheduledDateText = ref('')

// 日期格式化
const dateFormatter = (type: string, value: string) => {
  if (type === 'year') return `${value}年`
  if (type === 'month') return `${value}月`
  if (type === 'day') return `${value}日`
  if (type === 'hour') return `${value}时`
  if (type === 'minute') return `${value}分`
  return value
}

// 监听工程师类型变化
watch(engineerSelectType, (newType) => {
  formData.newEngineerId = ''
  if (newType === 'specified') {
    loadSpecifiedEngineers()
  } else {
    loadOtherEngineers()
  }
})

// 加载指定部门工程师
const loadSpecifiedEngineers = async () => {
  if (specifiedEngineers.value.length > 0) return
  
  engineerLoading.value = true
  try {
    const res = await uni.$u.http.get('/software/engineerSampleOrder/engineersByDifficultyLevel', {
      params: {
        difficultyLevelId: formData.difficultyLevelId,
        categoryId: formData.categoryId
      }
    })
    if (res.code === 200) {
      specifiedEngineers.value = res.data || []
    }
  } catch (error) {
    console.error('加载指定工程师失败:', error)
  } finally {
    engineerLoading.value = false
  }
}

// 加载其他部门工程师
const loadOtherEngineers = async () => {
  if (otherEngineers.value.length > 0) return
  
  engineerLoading.value = true
  try {
    const res = await uni.$u.http.get('/software/engineerSampleOrder/researchDepartmentsUser')
    if (res.code === 200) {
      otherEngineers.value = res.data || []
    }
  } catch (error) {
    console.error('加载其他工程师失败:', error)
  } finally {
    engineerLoading.value = false
  }
}

// 处理工程师选择
const handleEngineerChange = (value: string) => {
  formData.newEngineerId = value
}

// 打开日期选择器
const openDatePicker = () => {
  datetimePicker.value.open()
}

// 处理日期确认
const handleDateConfirm = (value: any) => {
  formData.scheduledDate = dayjs(value.value).format('YYYY-MM-DD HH:mm:ss')
  scheduledDateText.value = dayjs(value.value).format('YYYY-MM-DD HH:mm')
}

// 打开对话框
const open = (data: any) => {
  // 设置表单数据
  formData.id = data.id
  formData.sampleOrderCode = data.sampleOrderCode
  formData.currentEngineerName = data.nickName || '未分配'
  formData.newEngineerId = ''
  formData.scheduledDate = ''
  formData.adjustWorkSchedule = 0
  formData.difficultyLevelId = data.difficultyLevelId
  formData.categoryId = data.categoryId
  
  // 重置UI状态
  engineerSelectType.value = 'specified'
  scheduledDateText.value = ''
  
  // 加载工程师数据
  loadSpecifiedEngineers()
  
  // 显示对话框
  changeEngineerDialog.value.open()
}

// 关闭对话框
const handleClose = () => {
  changeEngineerDialog.value.close()
  emit('close')
}

// 确认提交
const handleConfirm = async () => {
  // 表单验证
  if (!formData.newEngineerId) {
    uni.showToast({
      title: '请选择工程师',
      icon: 'none'
    })
    return
  }
  
  if (!formData.scheduledDate) {
    uni.showToast({
      title: '请选择日期',
      icon: 'none'
    })
    return
  }
  
  loading.value = true
  try {
    const res = await uni.$u.http.post('/software/engineerSampleOrder/changeEngineer', {
      id: formData.id,
      newEngineerId: formData.newEngineerId,
      scheduledDate: formData.scheduledDate,
      adjustWorkSchedule: formData.adjustWorkSchedule
    })
    
    if (res.code === 200) {
      uni.showToast({
        title: '操作成功',
        icon: 'success'
      })
      emit('confirm', formData)
      handleClose()
    } else {
      uni.showToast({
        title: res.msg || '操作失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.change-engineer-dialog {
  background: white;
  border-radius: 16rpx;
  width: 680rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);

  .dialog-header {
    padding: 32rpx 32rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2rpx solid #f0f0f0;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;

    .dialog-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #303133;
    }

    .close-btn {
      font-size: 32rpx;
      color: #909399;
      cursor: pointer;
      padding: 8rpx;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f5f5f5;
        color: #606266;
      }
    }
  }

  .dialog-content {
    padding: 0 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-container {
      .form-item {
        margin-bottom: 24rpx;

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #303133;
          margin-bottom: 12rpx;
          font-weight: 500;

          .required {
            color: #f56c6c;
            margin-left: 4rpx;
          }
        }

        .switch-container {
          display: flex;
          align-items: center;
          padding: 12rpx 0;
        }
      }
    }
  }

  .dialog-footer {
    padding: 24rpx 32rpx 32rpx;
    display: flex;
    gap: 20rpx;
    border-top: 2rpx solid #f0f0f0;
    margin-top: 24rpx;
  }
}
</style>
